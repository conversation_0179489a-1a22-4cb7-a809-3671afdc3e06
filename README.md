# campus-front-training

> 新版教育培训模块

## 基本信息

代码仓库：[module-training](http://**********:3000/CIP/module-training.git)

安装依赖：

```bash
npm i # 或
pnpm i
```

如果安装有问题，尝试加上`--legacy-peer-deps`

```bash
npm i --legacy-peer-deps
```

运行：

```bash
npm start # 或
pnpm start
```

## 代码编写说明

### ui库

[yth-ui](http://*********:8084) 主要使用
[antd](https://ant.design/components/overview-cn/) yth-ui无法实现就使用antd

### 文件目录

-demo // 本地预览文件
-deploy // 发布配置
-src // 项目内容
--assets // 资源目录
--locales // 国际化
--pages // 业务功能
--components // 组件
--service // 接口
--types // 类型

### 类型定义

<mark>项目中请保持对变量等进行类型定义！对any类型进行禁止处理了！不建议使用！！</mark>

### 文件命名

```javascript
// 文件以小驼峰形式命名
targetModule;

// 单文件组件以大驼峰形式命名
TodoList;

// 关联组件文件以父组件命名为前缀
Todo;
TodoSearch;
TodoButtons;
```

### 样式文件请使用`xxx.module.less`格式

示例:
test.module.less 文件

```less
.highlight {
  color: red;
}
```

test.tsx 文件

```tsx
import styles from './test.module.less';

const TestModule = () => {
  return <div className={styles.highlight}>高亮测试</div>;
};
export default TestModule;
```

样式文件内使用图片说明：

```
.bg {
	background: url("~@/assets/logo.png") no-repeat center;
}
```

### eslint配置

<mark>请保持本地开发中eslint开启，按照代码规范进行开发</mark>

## 部署说明

> 部署文件目录[deploy]

- dev devops相关配置文件
- Dockerfile 前端打包镜像配置
- nginx 项目对应的docker的nginx配置

## git 说明

<mark>提交代码之前请先拉取代码！！提交代码之前请先拉取代码！！提交代码之前请先拉取代码！!</mark>
<mark>本模版增加了相关规范的提交校验，提交时注意代码规范处理，需要特别处理的请先联系负责人</mark>

## 本地预览

- pages 中编写代码并 export
- 在外层 demo 中创建相关模块文件，并新建 usage.md 文件，参考 /demo/test/usage.md
- 在对应的 usage.md 文件中引入自己模块文件，重新运行项目，打开本地地址 localhost:3000 即可看到相应页面
- 本地预览端口配置在 /build.json 中,在 devServer 中配置 port

## 线上预览

- pages 中编写代码并 export
- 在 build.json 中配置 modules（位置为: plugins->modules）, key 为需要暴露的名称，value 为对应文件的地址
- 查看当前本地运行 ip 和端口，通过 ip+端口+文件名.js（此处文件名为 modules 中配置的暴露出的名称）访问，看到 js 文件内容说明该文件配置正确
- 进入快开平台，表单配置->代理配置，将 ip 和端口配置到模块上
- 在后台管理->菜单配置中，新增对应菜单，注意菜单地址是需要包含父节点地址的完整地址，否则访问不到
- 在模块参数中配置相应模块信息，配置保存后刷新并访问相应菜单即可看到本地代码在平台上的显示效果（注：如果同一个代理下有多个 ip 配置，这里需要多刷新两次即为本地最新代码），配置参数如下：

```javascript
接入类型：@/components/RouteComponent/index
// 注意此处moduleName需要和本地build.json中modules里配置的保持一致
// affinity_host=********* 为本地代理，本地代码发布线上之后，就需要去除affinity_host=*********
	{
		"moduleName":"Test",
		"urls":[
		"/gw/test-front/Test.js?affinity_host=本地ip",
		"/gw/test-front/Test.css?affinity_host=本地ip"
	]}
```

## 本地开发获取 history

```javascript
const TestModuel: React.FC = ({ history }) => {
  //console.log(history);
  // 当前 history 跳转的 action，有 PUSH、REPLACE 和 POP 三种类型
  history.push("/list?a=b");
  history.push({
    pathname: "/list",
    query: {
      a: "b",
    },
  });

  // 跳转到上一个路由
  history.goBack();
};
```
