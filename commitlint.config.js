module.exports = {
  extends: ['@commitlint/config-conventional'],
  rules: {
    'type-enum': [
      2,
      'always',
      [
        'feat', // ✨ 新功能
        'fix', // 🐛 修复 bug
        'docs', // 📝 文档更新
        'style', // 🎨 代码格式（不影响功能，例如空格、分号等）
        'refactor', // ♻️ 代码重构（既不是新增功能，也不是修 bug）
        'perf', // ⚡️ 性能优化
        'test', // ✅ 添加测试
        'chore', // 🔨 构建过程或辅助工具的变动
        'revert', // ⏪ 回滚 commit
      ],
    ],
    'subject-case': [0], // 不限制 subject 大小写
  },
};
