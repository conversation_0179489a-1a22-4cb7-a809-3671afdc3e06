.assessmentPersonnelContainer {
  width: 100%;
  height: 100%;
  
  .operationButton {
    margin-right: 8px;
  }
  
  .statusTag {
    font-size: 12px;
  }
  
  .personnelCount {
    font-weight: 500;
    
    .arranged {
      color: #52c41a;
    }
    
    .total {
      color: #1890ff;
    }
  }
}

.assessmentPersonnelModal {
  .formSection {
    margin-bottom: 24px;
  }
  
  .personnelSection {
    .sectionTitle {
      font-size: 16px;
      font-weight: 500;
      margin-bottom: 16px;
      color: #262626;
    }
    
    .addButton {
      margin-bottom: 16px;
    }
    
    .personnelList {
      .personnelItem {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px;
        border: 1px solid #d9d9d9;
        border-radius: 6px;
        margin-bottom: 8px;
        background-color: #fafafa;
        
        .personnelInfo {
          flex: 1;
          
          .name {
            font-weight: 500;
            color: #262626;
          }
          
          .details {
            font-size: 12px;
            color: #8c8c8c;
            margin-top: 4px;
          }
        }
        
        .personnelActions {
          .deleteButton {
            color: #ff4d4f;
            border: none;
            background: none;
            cursor: pointer;
            padding: 4px 8px;
            border-radius: 4px;
            
            &:hover {
              background-color: #fff2f0;
            }
          }
        }
      }
    }
  }
  
  .modalFooter {
    margin-top: 24px;
    text-align: right;
    border-top: 1px solid #f0f0f0;
    padding-top: 16px;
    
    .footerButton {
      margin-left: 8px;
    }
  }
}

.personnelSelectModal {
  .searchForm {
    margin-bottom: 16px;
    padding: 16px;
    background-color: #fafafa;
    border-radius: 6px;
  }
  
  .personnelTable {
    .selectedRow {
      background-color: #e6f7ff;
    }
  }
  
  .selectedCount {
    margin: 16px 0;
    padding: 8px 12px;
    background-color: #f6ffed;
    border: 1px solid #b7eb8f;
    border-radius: 4px;
    color: #52c41a;
    
    .count {
      font-weight: 500;
    }
  }
}
