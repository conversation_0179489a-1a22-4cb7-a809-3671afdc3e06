import { trainRequest } from '@/request';

// ===== 类型定义 =====

/**
 * 通用 API 响应接口
 */
export type ApiResponse<T = unknown> = {
  code: number;
  data: T;
  msg?: string;
  total?: number;
};

/**
 * 分页查询参数接口
 */
interface PageQueryParams<T = unknown> {
  aescs?: string[];
  descs?: string[];
  condition: T | Record<string, unknown>;
  currentPage: number;
  pageSize: number;
}

/**
 * 考核人员安排查询参数接口
 */
export interface AssessmentPersonnelQueryParams {
  /** 考核名称 */
  assessmentName?: string;
  /** 考核类型 */
  assessmentType?: string;
  /** 考核状态 */
  assessmentStatus?: string;
  /** 开始时间 */
  startTime?: string;
  /** 结束时间 */
  endTime?: string;
  /** 组织机构ID */
  unitId?: string;
}

/**
 * 考核人员安排数据接口
 */
export interface AssessmentPersonnel {
  /** 主键 */
  id: string;
  /** 考核名称 */
  assessmentName: string;
  /** 考核类型 */
  assessmentType: string;
  /** 考核类型文本 */
  assessmentTypeText?: string;
  /** 考核状态 1-待开始 2-进行中 3-已结束 */
  assessmentStatus: string;
  /** 考核状态文本 */
  assessmentStatusText?: string;
  /** 考核开始时间 */
  startTime: string;
  /** 考核结束时间 */
  endTime: string;
  /** 考核描述 */
  description?: string;
  /** 考核人员数量 */
  personnelCount: number;
  /** 已安排人员数量 */
  arrangedCount: number;
  /** 组织机构ID */
  unitId: string;
  /** 组织机构名称 */
  unitName: string;
  /** 创建人 */
  createBy?: string;
  /** 创建时间 */
  createDate?: string;
  /** 更新人 */
  updateBy?: string;
  /** 更新时间 */
  updateDate?: string;
  /** 序号 */
  serialNo?: number;
}

/**
 * 考核人员详情数据接口
 */
export interface AssessmentPersonnelDetail {
  /** 主键 */
  id: string;
  /** 考核安排ID */
  assessmentId: string;
  /** 人员ID */
  personnelId: string;
  /** 人员姓名 */
  personnelName: string;
  /** 人员工号 */
  personnelCode: string;
  /** 人员部门 */
  departmentName: string;
  /** 人员岗位 */
  postName: string;
  /** 联系电话 */
  phone?: string;
  /** 邮箱 */
  email?: string;
  /** 安排状态 1-已安排 2-已确认 3-已完成 */
  arrangeStatus: string;
  /** 安排状态文本 */
  arrangeStatusText?: string;
  /** 安排时间 */
  arrangeTime: string;
  /** 确认时间 */
  confirmTime?: string;
  /** 完成时间 */
  completeTime?: string;
  /** 备注 */
  remark?: string;
  /** 序号 */
  serialNo?: number;
}

/**
 * 考核人员安排保存参数接口
 */
export interface AssessmentPersonnelSaveParams {
  /** 主键，新增时为空 */
  id?: string;
  /** 考核名称 */
  assessmentName: string;
  /** 考核类型 */
  assessmentType: string;
  /** 考核开始时间 */
  startTime: string;
  /** 考核结束时间 */
  endTime: string;
  /** 考核描述 */
  description?: string;
  /** 组织机构ID */
  unitId: string;
  /** 考核人员列表 */
  personnelList: {
    personnelId: string;
    personnelName: string;
    remark?: string;
  }[];
}

/**
 * 考核人员安排 API 类型定义
 */
type AssessmentApi = {
  /** 查询考核人员安排列表 */
  queryAssessmentPersonnelList: (
    data: PageQueryParams<AssessmentPersonnelQueryParams>,
  ) => Promise<ApiResponse<AssessmentPersonnel[]>>;

  /** 查询考核人员详情列表 */
  queryAssessmentPersonnelDetailList: (
    data: PageQueryParams<{ assessmentId: string }>,
  ) => Promise<ApiResponse<AssessmentPersonnelDetail[]>>;

  /** 保存考核人员安排 */
  saveAssessmentPersonnel: (data: AssessmentPersonnelSaveParams) => Promise<ApiResponse<boolean>>;

  /** 删除考核人员安排 */
  deleteAssessmentPersonnel: (id: string) => Promise<ApiResponse<boolean>>;

  /** 获取考核人员安排详情 */
  getAssessmentPersonnelDetail: (id: string) => Promise<ApiResponse<AssessmentPersonnel>>;
};

/**
 * 考核人员安排模块 API
 */
const assessmentApi: AssessmentApi = {
  /** 查询考核人员安排列表 */
  queryAssessmentPersonnelList: (data) => {
    return trainRequest.post('/assessment/personnel/page', { data });
  },

  /** 查询考核人员详情列表 */
  queryAssessmentPersonnelDetailList: (data) => {
    return trainRequest.post('/assessment/personnel/detail/page', { data });
  },

  /** 保存考核人员安排 */
  saveAssessmentPersonnel: (data) => {
    return trainRequest.post('/assessment/personnel/save', { data });
  },

  /** 删除考核人员安排 */
  deleteAssessmentPersonnel: (id) => {
    return trainRequest.delete(`/assessment/personnel/${id}`);
  },

  /** 获取考核人员安排详情 */
  getAssessmentPersonnelDetail: (id) => {
    return trainRequest.get(`/assessment/personnel/${id}`);
  },
};

export default assessmentApi;
